* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    min-height: 100vh;
    color: white;
    overflow-x: hidden;
}

/* 主容器 */
.main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 60px;
}

/* 测速区域 */
.speed-test-area {
    display: flex;
    align-items: center;
    gap: 120px; /* 增加间距，相当于5个字的距离 */
    margin-bottom: 40px;
    justify-content: flex-start;
    margin-left: -100px; /* 向左移动 */
}

.speed-button-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 测速按钮 */
.speed-test-button {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: linear-gradient(135deg, #00d4aa, #00b894);
    border: none;
    color: white;
    font-size: 24px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 212, 170, 0.3);
    z-index: 2;
}

.speed-test-button:hover {
    transform: translate(-50%, -50%) scale(1.05);
    box-shadow: 0 15px 40px rgba(0, 212, 170, 0.4);
}

.speed-test-button:active {
    transform: translate(-50%, -50%) scale(0.95);
}

/* 测速圆环 */
.speed-circle {
    position: absolute;
    top: 0;
    left: 0;
    width: 200px;
    height: 200px;
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 3;
}

.speed-circle.active {
    display: flex;
}

/* 确保按钮和圆环完全重叠 */
.speed-test-button,
.speed-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* 进度圆环 */
.progress-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.progress-svg {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    stroke-dasharray: 534.07; /* 2 * π * 85 */
    stroke-dashoffset: 534.07;
    transition: stroke-dashoffset 0.5s ease;
    stroke-linecap: round;
    filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.5));
}

/* 速度显示 */
.speed-display {
    text-align: center;
    z-index: 4;
}

.speed-value {
    font-size: 36px;
    font-weight: 700;
    color: #00ffff;
    margin-bottom: 4px;
    text-shadow: 0 0 20px rgba(0, 255, 255, 0.6);
    line-height: 1;
}

.speed-unit {
    font-size: 16px;
    color: #ffffff;
    font-weight: 500;
}

/* 右侧信息面板 */
.speed-info-panel {
    display: flex;
    flex-direction: column;
    gap: 30px;
    min-width: 200px;
}

.speed-info-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-label {
    font-size: 16px;
    color: #7fb3d3;
    font-weight: 500;
}

.info-value {
    font-size: 32px;
    font-weight: 700;
    color: #00ffff;
    text-shadow: 0 0 15px rgba(0, 255, 255, 0.4);
}

/* 延迟信息 */
.ping-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.ping-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.ping-label {
    color: #7fb3d3;
    min-width: 60px;
}

.ping-icon {
    font-size: 8px;
}

.ping-value {
    color: #ffffff;
    font-weight: 600;
}

.packet-loss-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    margin-top: 10px;
    gap: 8px;
    justify-content: flex-start;
}

.packet-loss-label {
    color: #7fb3d3;
    margin-right: 4px;
}

.packet-loss-value {
    color: #ffffff;
    font-weight: 600;
    margin-left: 0;
}

/* IP信息区域 */
.ip-info-section {
    display: flex;
    gap: 60px;
    margin-bottom: 40px;
}

.ip-info-item {
    display: flex;
    align-items: center;
    gap: 16px;
}

.ip-icon {
    font-size: 24px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.ip-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.ip-address {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
}

.ip-location {
    font-size: 14px;
    color: #7fb3d3;
}

/* 网络连通性测试 */
.connectivity-section {
    width: 100%;
    max-width: 800px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 20px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.refresh-icon {
    font-size: 16px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.refresh-icon:hover {
    transform: rotate(180deg);
}

.connectivity-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
}

.connectivity-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 16px 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 90px;
    padding: 18px 8px 10px 8px;
}

.connectivity-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.connectivity-item img {
    display: none;
}

.service-name {
    font-size: 16px;
    color: #00ffff;
    font-weight: 600;
    margin-bottom: 2px;
}

.ping-result {
    font-size: 14px;
    color: #00ffff;
    font-weight: 600;
}

.signal-bars {
    display: flex;
    gap: 2px;
    align-items: end;
}

.bar {
    width: 3px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 1px;
}

.bar:nth-child(1) { height: 6px; }
.bar:nth-child(2) { height: 9px; }
.bar:nth-child(3) { height: 12px; }
.bar:nth-child(4) { height: 15px; }

.bar.active {
    background: #00ffff;
}

/* 居中测速区域 */
.centered-area {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 80px;
    margin: 0 auto 40px auto;
    flex-wrap: wrap;
    gap: 192px; /* 调整测速按钮与右侧信息面板的距离为12个汉字宽度（约192px） */
}

/* 优化测速信息面板间距 */
.speed-info-panel {
    gap: 24px;
    min-width: 220px;
}

.speed-info-item .info-label {
    font-size: 18px;
    color: #7fd3b3;
    margin-bottom: 2px;
}

.speed-info-item .info-value {
    font-size: 36px;
    color: #00ffff;
    margin-bottom: 2px;
}

.ping-info .ping-item {
    gap: 4px;
    font-size: 16px;
    margin-bottom: 2px;
}

.ping-label, .ping-unit {
    color: #7fb3d3;
    font-size: 14px;
}

/* IP信息区域美化+居中 */
.ip-info-centered {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 60px;
    margin: 0 auto 40px auto;
    flex-wrap: wrap;
}

.ip-info-item .ip-address, .ip-info-item .ip-location {
    font-size: 18px;
    color: #fff;
    margin-bottom: 2px;
}

.ip-info-item .ip-location {
    color: #7fb3d3;
}

/* 网络连通性测试底部美化 */
.connectivity-item {
    min-width: 90px;
    padding: 18px 8px 10px 8px;
}

.connectivity-section {
    margin: 0 auto 30px auto;
}

.connectivity-grid {
    justify-items: center;
}

/* speedtest.cn风格美化 */
.speedtest-cn-layout {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100vw;
    min-height: 100vh;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    color: #fff;
}
.speedtest-cn-center {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-top: 60px;
    gap: 80px;
}
.speedtest-cn-circle-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.speedtest-cn-button {
    width: 220px;
    height: 220px;
    border-radius: 50%;
    background: linear-gradient(135deg, #00d4aa, #00b894);
    border: none;
    color: #fff;
    font-size: 32px;
    font-weight: bold;
    box-shadow: 0 10px 30px rgba(0, 212, 170, 0.3);
    margin-bottom: 24px;
    cursor: pointer;
    transition: all 0.3s;
}
.speedtest-cn-button:hover {
    transform: scale(1.05);
}
.speedtest-cn-circle-info {
    text-align: center;
}
.speedtest-cn-value {
    font-size: 56px;
    color: #00ffff;
    font-weight: bold;
    margin-bottom: 4px;
}
.speedtest-cn-unit {
    font-size: 20px;
    color: #b8eaff;
}
.speedtest-cn-info-panel {
    display: flex;
    flex-direction: column;
    gap: 18px;
    min-width: 200px;
}
.speedtest-cn-info-row {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 20px;
}
.speedtest-cn-label {
    color: #b8eaff;
    min-width: 60px;
}
.speedtest-cn-num {
    color: #00ffff;
    font-size: 28px;
    font-weight: bold;
    min-width: 60px;
    text-align: right;
}
.speedtest-cn-ip-area {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 40px;
    margin: 40px 0 24px 0;
}
.speedtest-cn-ip-block {
    background: rgba(255,255,255,0.06);
    border-radius: 8px;
    padding: 10px 18px;
    font-size: 16px;
    color: #fff;
    display: flex;
    align-items: center;
    gap: 6px;
}
.speedtest-cn-ip-label {
    color: #b8eaff;
}
.speedtest-cn-ip-value {
    color: #00ffff;
    font-weight: bold;
}
.speedtest-cn-connectivity {
    width: 100vw;
    max-width: 900px;
    margin: 0 auto 40px auto;
}
.speedtest-cn-section-title {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 24px;
    color: #fff;
}
.speedtest-cn-connectivity-grid {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 18px;
}
.speedtest-cn-connectivity-item {
    background: rgba(255,255,255,0.06);
    border-radius: 16px;
    padding: 24px 32px 18px 32px;
    min-width: 120px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    font-size: 18px;
}
.speedtest-cn-connectivity-item .service-name {
    color: #00ffff;
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 8px;
}
.speedtest-cn-connectivity-item .ping-result {
    color: #00ffff;
    font-size: 16px;
    margin-bottom: 8px;
}
.signal-bars {
    display: flex;
    gap: 2px;
    align-items: end;
}
.bar {
    width: 4px;
    background: rgba(255,255,255,0.3);
    border-radius: 1px;
}
.bar:nth-child(1) { height: 8px; }
.bar:nth-child(2) { height: 14px; }
.bar:nth-child(3) { height: 18px; }
.bar:nth-child(4) { height: 22px; }
.bar.active {
    background: #00ffff;
}
@media (max-width: 900px) {
    .speedtest-cn-center { flex-direction: column; gap: 40px; }
    .speedtest-cn-connectivity-grid { flex-direction: column; gap: 12px; }
    .speedtest-cn-ip-area { flex-direction: column; gap: 16px; }
}

/* 动画效果 */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 255, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 20px rgba(0, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 255, 255, 0);
    }
}

.speed-button.testing {
    animation: pulse 2s infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.testing .refresh-icon {
    animation: rotate 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .ip-info {
        flex-direction: column;
        gap: 20px;
        align-items: center;
    }
    
    .server-info {
        gap: 30px;
    }
    
    .test-items {
        flex-direction: column;
    }
    
    .test-item {
        min-width: auto;
    }
}

/* 隐藏按钮文字当测试开始时 */
.speed-button.testing .button-text {
    display: none;
}

/* 测试状态指示器 */
.status-indicator {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #00ff00;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 结果显示优化 */
.speed-value.completed {
    color: #00ff88;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
}

/* 加载动画 */
.loading-dots::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* 改进的悬停效果 */
.test-item:hover .test-ping {
    color: #ffffff;
    transform: scale(1.1);
}

/* 连接状态颜色 */
.test-ping.good { color: #00ff88; }
.test-ping.medium { color: #ffaa00; }
.test-ping.poor { color: #ff4444; }

/* 移除网络连通性测试图标相关样式 */
.connectivity-item .service-icon {
    display: none !important;
}

/* 调整丢包信息位置 */
.dynamic-loss-info {
    margin-left: 32px;
    gap: 8px;
    justify-content: flex-start;
}

.dynamic-loss-info .packet-loss-label {
    margin-right: 4px;
}

.dynamic-loss-info .packet-loss-value {
    margin-left: 0;
}
