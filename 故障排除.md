# 网络测速工具 - 故障排除指南

## 🚨 常见问题及解决方案

### 1. 端口占用错误 (EADDRINUSE)

**错误信息：**
```
Error: listen EADDRINUSE: address already in use :::3000
```

**解决方案：**

#### 方法一：使用停止脚本（推荐）
1. 双击运行 `stop-server.bat`
2. 按提示终止占用端口的进程
3. 重新启动服务器

#### 方法二：手动查找并终止进程
1. 打开命令提示符（管理员模式）
2. 查找占用端口的进程：
   ```cmd
   netstat -ano | findstr :3000
   ```
3. 记下进程ID（PID）
4. 终止进程：
   ```cmd
   taskkill /pid <PID> /f
   ```

#### 方法三：重启电脑
如果以上方法都不行，重启电脑可以清除所有进程。

### 2. Node.js 未安装

**错误信息：**
```
'node' 不是内部或外部命令
```

**解决方案：**
1. 访问 [Node.js官网](https://nodejs.org/)
2. 下载并安装LTS版本
3. 重启命令提示符
4. 验证安装：`node --version`

### 3. 依赖安装失败

**错误信息：**
```
npm ERR! network request failed
```

**解决方案：**

#### 方法一：更换npm源
```cmd
npm config set registry https://registry.npmmirror.com
npm install
```

#### 方法二：清除缓存
```cmd
npm cache clean --force
npm install
```

#### 方法三：删除node_modules重新安装
```cmd
rmdir /s node_modules
npm install
```

### 4. 浏览器无法访问

**可能原因：**
- 防火墙阻止
- 端口被其他程序占用
- 服务器未正常启动

**解决方案：**
1. 检查服务器是否正常启动（看到启动成功信息）
2. 尝试不同的浏览器
3. 检查防火墙设置
4. 尝试使用 `127.0.0.1:3000` 而不是 `localhost:3000`

### 5. 测速结果异常

**可能原因：**
- 网络不稳定
- 其他程序占用带宽
- 服务器性能限制

**解决方案：**
1. 关闭其他占用网络的程序
2. 使用有线网络连接
3. 多次测试取平均值
4. 检查网络环境是否稳定

### 6. 连通性测试失败

**现象：**
所有网站显示"测试中"或异常高的延迟

**解决方案：**
1. 检查网络连接
2. 暂时关闭VPN或代理
3. 检查DNS设置
4. 这是正常现象，不影响主要测速功能

## 🛠 高级故障排除

### 查看详细错误日志

1. 打开浏览器开发者工具（F12）
2. 查看Console标签页的错误信息
3. 查看Network标签页的网络请求状态

### 手动测试API端点

在浏览器中直接访问：
- `http://localhost:3000/ping` - 测试ping接口
- `http://localhost:3000/server-info` - 查看服务器信息

### 重置应用到初始状态

1. 删除 `node_modules` 文件夹
2. 删除 `package-lock.json` 文件
3. 运行 `npm install`
4. 重新启动服务器

## 📞 获取帮助

如果以上方法都无法解决问题：

1. 记录完整的错误信息
2. 记录操作系统版本和Node.js版本
3. 描述具体的操作步骤
4. 提供浏览器控制台的错误信息

## 🔧 系统要求

- **操作系统：** Windows 10/11, macOS 10.14+, Linux
- **Node.js：** 14.0.0 或更高版本
- **浏览器：** Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **内存：** 至少 512MB 可用内存
- **网络：** 稳定的互联网连接

## ⚡ 性能优化建议

1. **关闭不必要的程序** - 释放系统资源
2. **使用有线连接** - 获得更稳定的网络性能
3. **选择合适的测试时间** - 避开网络高峰期
4. **定期清理浏览器缓存** - 确保测试准确性
