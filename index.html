<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络测速工具</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="main-container speedtest-cn-layout">
        <div class="speedtest-cn-center">
            <div class="speedtest-cn-circle-area">
                <button id="startTest" class="speedtest-cn-button">
                    <span class="button-text">测速</span>
                </button>
                <div class="progress-ring" id="progressRing">
                    <svg class="progress-svg" width="200" height="200">
                        <circle class="progress-ring-circle" cx="100" cy="100" r="85"
                                stroke="#00ffff" stroke-width="6" fill="transparent"/>
                    </svg>
                </div>
                <div class="speedtest-cn-circle-info">
                    <div class="speedtest-cn-value" id="speedValue">0</div>
                    <div class="speedtest-cn-unit">Mbps</div>
                </div>
            </div>
            <div class="speedtest-cn-info-panel">
                <div class="speedtest-cn-info-row">
                    <span class="speedtest-cn-label">下行</span>
                    <span class="speedtest-cn-num" id="downloadSpeed">--</span>
                    <span class="speedtest-cn-unit">Mbps</span>
                </div>
                <div class="speedtest-cn-info-row">
                    <span class="speedtest-cn-label">上行</span>
                    <span class="speedtest-cn-num" id="uploadSpeed">--</span>
                    <span class="speedtest-cn-unit">Mbps</span>
                </div>
                <div class="speedtest-cn-info-row">
                    <span class="speedtest-cn-label">时延</span>
                    <span class="speedtest-cn-num" id="pingValue">--</span>
                    <span class="speedtest-cn-unit">ms</span>
                </div>
                <div class="speedtest-cn-info-row">
                    <span class="speedtest-cn-label">抖动</span>
                    <span class="speedtest-cn-num" id="jitterValue">--</span>
                    <span class="speedtest-cn-unit">ms</span>
                </div>
                <div class="speedtest-cn-info-row">
                    <span class="speedtest-cn-label">丢包率</span>
                    <span class="speedtest-cn-num" id="lossValue">--</span>
                    <span class="speedtest-cn-unit">%</span>
                </div>
            </div>
        </div>
        <div class="speedtest-cn-ip-area">
            <div class="speedtest-cn-ip-block">
                <span class="speedtest-cn-ip-label">IP地址：</span>
                <span class="speedtest-cn-ip-value" id="ipAddress">获取中...</span>
            </div>
            <div class="speedtest-cn-ip-block">
                <span class="speedtest-cn-ip-label">运营商：</span>
                <span class="speedtest-cn-ip-value" id="ipProvider">--</span>
            </div>
            <div class="speedtest-cn-ip-block">
                <span class="speedtest-cn-ip-label">省份：</span>
                <span class="speedtest-cn-ip-value" id="ipRegion">--</span>
            </div>
            <div class="speedtest-cn-ip-block">
                <span class="speedtest-cn-ip-label">城市：</span>
                <span class="speedtest-cn-ip-value" id="ipCity">--</span>
            </div>
        </div>
        <div class="speedtest-cn-connectivity">
            <h3 class="speedtest-cn-section-title">网络连通性测试 <span class="refresh-icon" id="refreshConnectivity">🔄</span></h3>
            <div class="speedtest-cn-connectivity-grid">
                <div class="speedtest-cn-connectivity-item">
                    <span class="service-name">百度</span>
                    <span class="ping-result" id="ping-baidu">-- ms</span>
                    <div class="signal-bars" id="bars-baidu">
                        <div class="bar"></div><div class="bar"></div><div class="bar"></div><div class="bar"></div>
                    </div>
                </div>
                <div class="speedtest-cn-connectivity-item">
                    <span class="service-name">腾讯QQ</span>
                    <span class="ping-result" id="ping-qq">-- ms</span>
                    <div class="signal-bars" id="bars-qq">
                        <div class="bar"></div><div class="bar"></div><div class="bar"></div><div class="bar"></div>
                    </div>
                </div>
                <div class="speedtest-cn-connectivity-item">
                    <span class="service-name">淘宝</span>
                    <span class="ping-result" id="ping-taobao">-- ms</span>
                    <div class="signal-bars" id="bars-taobao">
                        <div class="bar"></div><div class="bar"></div><div class="bar"></div><div class="bar"></div>
                    </div>
                </div>
                <div class="speedtest-cn-connectivity-item">
                    <span class="service-name">微信</span>
                    <span class="ping-result" id="ping-wechat">-- ms</span>
                    <div class="signal-bars" id="bars-wechat">
                        <div class="bar"></div><div class="bar"></div><div class="bar"></div><div class="bar"></div>
                    </div>
                </div>
                <div class="speedtest-cn-connectivity-item">
                    <span class="service-name">抖音</span>
                    <span class="ping-result" id="ping-douyin">-- ms</span>
                    <div class="signal-bars" id="bars-douyin">
                        <div class="bar"></div><div class="bar"></div><div class="bar"></div><div class="bar"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="speedtest.js"></script>
    <script>
    // 只在点击测速按钮后才更新数据
    function updatePingInfoByResult(result) {
        document.getElementById('pingValue').textContent = result.ping !== undefined ? result.ping : '--';
        document.getElementById('jitterValue').textContent = result.jitter !== undefined ? result.jitter : '--';
        document.getElementById('lossValue').textContent = result.loss !== undefined ? result.loss : '--';
    }
    document.getElementById('startTest').onclick = async function() {
        // 这里应调用真实测速逻辑，以下为演示用假数据
        // 你可以将 result 换成真实测速返回值
        const result = { ping: 7, jitter: 16, loss: 3.83 };
        updatePingInfoByResult(result);
    };
    </script>
</body>
</html>
