const express = require('express');
const path = require('path');
const crypto = require('crypto');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(express.static('.'));
app.use(express.raw({ limit: '50mb' }));

// CORS支持
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, HEAD, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// 主页
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Ping测试端点
app.head('/ping', (req, res) => {
    res.status(200).end();
});

app.get('/ping', (req, res) => {
    res.json({ timestamp: Date.now() });
});

// 下载测试端点
app.get('/download-test', (req, res) => {
    const size = parseInt(req.query.size) || 10 * 1024 * 1024; // 默认10MB
    
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Length', size);
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    
    // 生成随机数据流
    let sent = 0;
    const chunkSize = 64 * 1024; // 64KB chunks
    
    const sendChunk = () => {
        if (sent >= size) {
            res.end();
            return;
        }
        
        const remainingSize = Math.min(chunkSize, size - sent);
        const chunk = crypto.randomBytes(remainingSize);
        
        res.write(chunk);
        sent += remainingSize;
        
        // 使用setImmediate来避免阻塞事件循环
        setImmediate(sendChunk);
    };
    
    sendChunk();
});

// 上传测试端点
app.post('/upload-test', (req, res) => {
    let receivedBytes = 0;
    
    req.on('data', (chunk) => {
        receivedBytes += chunk.length;
    });
    
    req.on('end', () => {
        res.json({
            success: true,
            receivedBytes: receivedBytes,
            timestamp: Date.now()
        });
    });
    
    req.on('error', (err) => {
        console.error('Upload error:', err);
        res.status(500).json({ error: 'Upload failed' });
    });
});

// 获取服务器信息
app.get('/server-info', (req, res) => {
    res.json({
        timestamp: Date.now(),
        server: 'SpeedTest Server',
        version: '1.0.0'
    });
});

// 错误处理
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({ error: 'Not found' });
});

// 启动服务器，如果端口被占用则尝试其他端口
function startServer(port) {
    const server = app.listen(port, () => {
        console.log(`测速服务器运行在 http://localhost:${port}`);
        console.log('按 Ctrl+C 停止服务器');
        console.log('');
        console.log('如果浏览器没有自动打开，请手动访问上述地址');
    });

    server.on('error', (err) => {
        if (err.code === 'EADDRINUSE') {
            console.log(`端口 ${port} 已被占用，尝试端口 ${port + 1}...`);
            startServer(port + 1);
        } else {
            console.error('服务器启动失败:', err);
            process.exit(1);
        }
    });

    return server;
}

const server = startServer(PORT);

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('\n正在关闭服务器...');
    server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
    });
});
