@echo off
chcp 936 >nul
title Speed Test Tool
color 0A

echo ========================================
echo           Speed Test Tool v1.0
echo ========================================
echo.

REM Check Node.js installation
echo [1/4] Checking Node.js environment...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js not detected
    echo Please install Node.js: https://nodejs.org/
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do echo Node.js version: %%i
)

REM Check dependencies
echo [2/4] Checking project dependencies...
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo Dependency installation failed, please check network connection
        pause
        exit /b 1
    )
    echo Dependencies installed successfully
) else (
    echo Dependencies already exist
)

REM Check port usage
echo [3/4] Checking port usage...
netstat -ano | findstr :3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo Port 3000 is occupied, server will automatically select another port
)

echo [4/4] Starting server...
echo.
echo Starting speed test server...
echo Server will display access address after startup
echo Press Ctrl+C to stop server
echo.

npm start

echo.
echo Server stopped
pause
