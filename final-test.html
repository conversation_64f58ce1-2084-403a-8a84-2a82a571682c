<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .test-container { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .status { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        iframe { width: 100%; height: 600px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>网络测速工具 - 最终功能测试</h1>
    
    <div class="test-container">
        <h2>自动化测试结果</h2>
        <div id="test-results"></div>
        <button onclick="runAllTests()">重新运行所有测试</button>
    </div>
    
    <div class="test-container">
        <h2>主应用预览</h2>
        <p>在下面的iframe中测试点击"测速"按钮：</p>
        <iframe src="/" id="mainApp"></iframe>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const container = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        async function testServerEndpoints() {
            addResult('🔍 开始测试服务器端点...', 'info');
            
            try {
                // 测试主页
                const homeRes = await fetch('/');
                addResult(`✅ 主页访问: ${homeRes.status === 200 ? '成功' : '失败 (状态码: ' + homeRes.status + ')'}`, 
                    homeRes.status === 200 ? 'success' : 'error');
                
                // 测试ping端点
                const pingRes = await fetch('/ping');
                const pingData = await pingRes.json();
                addResult(`✅ Ping端点: 成功 (时间戳: ${pingData.timestamp})`, 'success');
                
                // 测试下载端点
                const downloadRes = await fetch('/download-test?size=1024');
                addResult(`✅ 下载端点: ${downloadRes.status === 200 ? '成功' : '失败'}`, 
                    downloadRes.status === 200 ? 'success' : 'error');
                
                // 测试上传端点
                const uploadRes = await fetch('/upload-test', {
                    method: 'POST',
                    body: new ArrayBuffer(1024),
                    headers: { 'Content-Type': 'application/octet-stream' }
                });
                const uploadData = await uploadRes.json();
                addResult(`✅ 上传端点: ${uploadData.success ? '成功' : '失败'}`, 
                    uploadData.success ? 'success' : 'error');
                
            } catch (error) {
                addResult(`❌ 服务器端点测试失败: ${error.message}`, 'error');
            }
        }

        function testFrontendElements() {
            addResult('🔍 开始测试前端元素...', 'info');
            
            // 通过iframe访问主应用的DOM
            const iframe = document.getElementById('mainApp');
            
            iframe.onload = function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    
                    const elements = [
                        'startTest', 'speedCircle', 'speedValue', 'speedType', 'ipAddress',
                        'lolPing', 'douyinPing', 'jdPing', 'msPing', 'toutiaoPing'
                    ];
                    
                    let allFound = true;
                    elements.forEach(id => {
                        const element = iframeDoc.getElementById(id);
                        const found = element !== null;
                        if (!found) allFound = false;
                        addResult(`${found ? '✅' : '❌'} DOM元素 ${id}: ${found ? '存在' : '缺失'}`, 
                            found ? 'success' : 'error');
                    });
                    
                    // 检查SpeedTest类
                    const speedTestExists = iframe.contentWindow.SpeedTest !== undefined;
                    const instanceExists = iframe.contentWindow.speedTestInstance !== undefined;
                    
                    addResult(`${speedTestExists ? '✅' : '❌'} SpeedTest类: ${speedTestExists ? '已定义' : '未定义'}`, 
                        speedTestExists ? 'success' : 'error');
                    addResult(`${instanceExists ? '✅' : '❌'} SpeedTest实例: ${instanceExists ? '已创建' : '未创建'}`, 
                        instanceExists ? 'success' : 'error');
                    
                    // 测试点击功能
                    const startButton = iframeDoc.getElementById('startTest');
                    if (startButton) {
                        addResult('🔍 测试点击功能...', 'info');
                        
                        // 检查点击事件监听器
                        const hasClickListener = startButton.onclick !== null || 
                            (iframe.contentWindow.speedTestInstance && 
                             typeof iframe.contentWindow.speedTestInstance.startSpeedTest === 'function');
                        
                        addResult(`${hasClickListener ? '✅' : '❌'} 点击事件监听器: ${hasClickListener ? '已设置' : '未设置'}`, 
                            hasClickListener ? 'success' : 'warning');
                        
                        if (hasClickListener) {
                            addResult('💡 点击测速按钮应该可以正常工作。请在上方iframe中手动测试。', 'info');
                        }
                    }
                    
                    addResult(`📊 总体状态: ${allFound && speedTestExists && instanceExists ? '所有功能正常' : '存在问题需要修复'}`, 
                        allFound && speedTestExists && instanceExists ? 'success' : 'warning');
                        
                } catch (error) {
                    addResult(`❌ 前端测试失败: ${error.message}`, 'error');
                }
            };
        }

        async function runAllTests() {
            document.getElementById('test-results').innerHTML = '';
            addResult('🚀 开始运行完整功能测试...', 'info');
            
            await testServerEndpoints();
            
            // 延迟测试前端元素，等待iframe加载
            setTimeout(testFrontendElements, 1000);
        }

        // 页面加载后自动运行测试
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 500);
        });
    </script>
</body>
</html>
