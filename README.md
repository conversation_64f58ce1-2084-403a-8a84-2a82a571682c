# 网络测速工具

一个现代化的网络测速应用，支持下载速度、上传速度和延迟测试。

## 功能特性

- 🚀 **下载速度测试** - 多线程并发下载测试
- 📤 **上传速度测试** - 多线程并发上传测试  
- 🏓 **Ping延迟测试** - 网络延迟和连通性测试
- 🌐 **连通性检测** - 测试到各大网站的连接状态
- 📱 **响应式设计** - 支持桌面和移动设备
- 🎨 **现代化UI** - 美观的渐变界面和动画效果

## 快速开始

### 方法一：使用启动脚本（推荐）

1. 双击运行 `start.bat` 文件
2. 脚本会自动检查Node.js环境并安装依赖
3. 服务器启动后，浏览器会自动打开应用

### 方法二：手动启动

1. **安装依赖**
```bash
npm install
```

2. **启动服务器**
```bash
npm start
```

3. **访问应用**
打开浏览器访问: http://localhost:3000

## 使用说明

1. **开始测速**：点击中央的蓝色"测速"按钮
2. **测试流程**：
   - 首先进行Ping延迟测试（约5秒）
   - 然后进行下载速度测试（10秒）
   - 最后进行上传速度测试（10秒）
3. **查看结果**：测试完成后显示平均网速和详细数据
4. **连通性测试**：页面下方实时显示到各大网站的连接延迟

## 开发模式

如果你想在开发过程中自动重启服务器：

```bash
npm run dev
```

## 技术实现

### 前端技术
- **HTML5** - 现代化的网页结构
- **CSS3** - 渐变背景、动画效果、响应式布局
- **JavaScript ES6+** - 异步编程、Fetch API、Web Workers

### 后端技术
- **Node.js** - 服务器运行环境
- **Express.js** - Web框架
- **Stream API** - 高效的数据传输

### 测速原理

#### 下载测试
1. 创建多个并发的HTTP GET请求
2. 服务器返回随机生成的二进制数据流
3. 客户端实时计算接收速度
4. 测试持续10秒，取平均值

#### 上传测试
1. 客户端生成测试数据
2. 创建多个并发的HTTP POST请求
3. 监听上传进度事件
4. 实时计算上传速度

#### Ping测试
1. 发送多个HTTP HEAD请求
2. 测量往返时间(RTT)
3. 计算平均延迟

## 项目结构

```
speedtest/
├── index.html          # 主页面
├── style.css           # 样式文件
├── speedtest.js        # 前端测速逻辑
├── server.js           # 后端服务器
├── package.json        # 项目配置
└── README.md          # 说明文档
```

## API接口

### GET /ping
测试网络延迟

### GET /download-test
下载测试数据流
- 参数: `size` (可选) - 数据大小，默认10MB

### POST /upload-test
上传测试接口
- 接收任意二进制数据
- 返回接收字节数和时间戳

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 功能特色

### 🎯 精确测速
- 多线程并发测试，更准确反映真实网络性能
- 自动补偿网络开销，提供更精确的速度数据
- 支持自定义测试时长和并发数

### 📊 实时监控
- 实时显示测速进度和当前速度
- 动态圆环进度条，直观展示测试状态
- 连通性测试自动更新，监控网络健康状态

### 💾 数据存储
- 自动保存最近10次测试历史
- 本地存储，保护用户隐私
- 支持测试结果导出和分享

### 🎨 用户体验
- 现代化渐变UI设计
- 流畅的动画效果和过渡
- 完全响应式，支持各种设备尺寸

## 故障排除

### 常见问题

**Q: 测速结果不准确怎么办？**
A: 请确保测试期间网络稳定，关闭其他占用带宽的应用程序。

**Q: 无法连接到服务器？**
A: 检查防火墙设置，确保端口3000未被占用。

**Q: 连通性测试显示异常？**
A: 这可能是由于CORS限制，属于正常现象，不影响主要测速功能。

### 性能优化建议

1. 使用有线网络连接以获得更稳定的测试结果
2. 关闭VPN和代理服务
3. 确保没有其他设备占用网络带宽
4. 选择网络使用较少的时间段进行测试

## 许可证

MIT License
