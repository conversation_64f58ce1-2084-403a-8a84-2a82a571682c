// 功能测试脚本
console.log('开始测试网络测速工具功能...');

// 测试服务器端点
async function testServerEndpoints() {
    const baseUrl = 'http://localhost:3000';
    
    console.log('\n=== 测试服务器端点 ===');
    
    // 测试主页
    try {
        const response = await fetch(baseUrl);
        console.log('✓ 主页访问:', response.status === 200 ? '成功' : '失败');
    } catch (error) {
        console.log('✗ 主页访问: 失败 -', error.message);
    }
    
    // 测试ping端点
    try {
        const response = await fetch(baseUrl + '/ping');
        const data = await response.json();
        console.log('✓ Ping端点:', data.timestamp ? '成功' : '失败');
    } catch (error) {
        console.log('✗ Ping端点: 失败 -', error.message);
    }
    
    // 测试下载端点
    try {
        const response = await fetch(baseUrl + '/download-test?size=1024');
        console.log('✓ 下载端点:', response.status === 200 ? '成功' : '失败');
    } catch (error) {
        console.log('✗ 下载端点: 失败 -', error.message);
    }
    
    // 测试上传端点
    try {
        const testData = new ArrayBuffer(1024);
        const response = await fetch(baseUrl + '/upload-test', {
            method: 'POST',
            body: testData,
            headers: {
                'Content-Type': 'application/octet-stream'
            }
        });
        const result = await response.json();
        console.log('✓ 上传端点:', result.success ? '成功' : '失败');
    } catch (error) {
        console.log('✗ 上传端点: 失败 -', error.message);
    }
}

// 测试前端功能
function testFrontendFunctionality() {
    console.log('\n=== 测试前端功能 ===');
    
    // 检查关键DOM元素
    const elements = [
        'startTest',
        'speedCircle', 
        'speedValue',
        'speedType',
        'ipAddress',
        'lolPing',
        'douyinPing',
        'jdPing',
        'msPing',
        'toutiaoPing'
    ];
    
    elements.forEach(id => {
        const element = document.getElementById(id);
        console.log(`${element ? '✓' : '✗'} 元素 ${id}:`, element ? '存在' : '缺失');
    });
    
    // 检查SpeedTest类是否正确初始化
    if (window.speedTestInstance) {
        console.log('✓ SpeedTest实例: 已初始化');
    } else {
        console.log('✗ SpeedTest实例: 未初始化');
    }
}

// 模拟点击测试
function simulateSpeedTest() {
    console.log('\n=== 模拟测速测试 ===');
    
    const startButton = document.getElementById('startTest');
    if (startButton) {
        console.log('✓ 找到测速按钮，模拟点击...');
        startButton.click();
        
        // 检查测试是否开始
        setTimeout(() => {
            const speedCircle = document.getElementById('speedCircle');
            if (speedCircle && speedCircle.classList.contains('active')) {
                console.log('✓ 测速已开始，圆环显示正常');
            } else {
                console.log('✗ 测速未开始或圆环显示异常');
            }
        }, 1000);
    } else {
        console.log('✗ 未找到测速按钮');
    }
}

// 运行测试
async function runTests() {
    await testServerEndpoints();
    
    // 如果在浏览器环境中运行
    if (typeof document !== 'undefined') {
        testFrontendFunctionality();
        
        // 等待页面完全加载后再进行模拟测试
        setTimeout(simulateSpeedTest, 2000);
    }
    
    console.log('\n=== 测试完成 ===');
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testServerEndpoints };
    
    // 在Node.js中只运行服务器端点测试
    if (require.main === module) {
        const fetch = require('node-fetch');
        global.fetch = fetch;
        testServerEndpoints();
    }
} else {
    // 在浏览器中运行完整测试
    runTests();
}
