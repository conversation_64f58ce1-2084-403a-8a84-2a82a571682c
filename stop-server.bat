@echo off
chcp 65001 >nul
title 停止测速服务器
color 0C

echo ========================================
echo         停止网络测速服务器
echo ========================================
echo.

echo 正在查找占用端口3000的进程...

REM 查找占用端口3000的进程
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do (
    set pid=%%a
    goto :found
)

echo ✅ 端口3000未被占用
goto :end

:found
echo 找到进程ID: %pid%

REM 获取进程名称
for /f "tokens=1" %%b in ('tasklist /fi "pid eq %pid%" /fo csv /nh') do (
    set processname=%%b
    set processname=!processname:"=!
)

echo 进程名称: %processname%
echo.

REM 询问是否终止进程
set /p choice=是否终止该进程? (y/n): 
if /i "%choice%"=="y" (
    taskkill /pid %pid% /f >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ 进程已成功终止
    ) else (
        echo ❌ 终止进程失败，可能需要管理员权限
    )
) else (
    echo 操作已取消
)

:end
echo.
pause
