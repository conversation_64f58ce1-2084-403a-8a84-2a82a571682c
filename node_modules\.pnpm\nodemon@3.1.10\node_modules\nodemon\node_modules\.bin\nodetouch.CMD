@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\speetest\node_modules\.pnpm\touch@3.1.1\node_modules\touch\bin\node_modules;C:\Users\<USER>\Desktop\speetest\node_modules\.pnpm\touch@3.1.1\node_modules\touch\node_modules;C:\Users\<USER>\Desktop\speetest\node_modules\.pnpm\touch@3.1.1\node_modules;C:\Users\<USER>\Desktop\speetest\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\speetest\node_modules\.pnpm\touch@3.1.1\node_modules\touch\bin\node_modules;C:\Users\<USER>\Desktop\speetest\node_modules\.pnpm\touch@3.1.1\node_modules\touch\node_modules;C:\Users\<USER>\Desktop\speetest\node_modules\.pnpm\touch@3.1.1\node_modules;C:\Users\<USER>\Desktop\speetest\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\touch@3.1.1\node_modules\touch\bin\nodetouch.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\touch@3.1.1\node_modules\touch\bin\nodetouch.js" %*
)
