<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测速工具调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>网络测速工具调试页面</h1>
    
    <div class="debug-panel">
        <h2>服务器端点测试</h2>
        <button class="test-button" onclick="testPing()">测试Ping端点</button>
        <button class="test-button" onclick="testDownload()">测试下载端点</button>
        <button class="test-button" onclick="testUpload()">测试上传端点</button>
        <div id="server-results"></div>
    </div>
    
    <div class="debug-panel">
        <h2>前端功能测试</h2>
        <button class="test-button" onclick="testDOMElements()">检查DOM元素</button>
        <button class="test-button" onclick="testSpeedTestClass()">检查SpeedTest类</button>
        <button class="test-button" onclick="simulateClick()">模拟点击测速</button>
        <div id="frontend-results"></div>
    </div>
    
    <div class="debug-panel">
        <h2>实时日志</h2>
        <div id="console-log" style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;"></div>
    </div>

    <script>
        // 拦截console.log输出
        const originalLog = console.log;
        const originalError = console.error;
        const logDiv = document.getElementById('console-log');
        
        function addToLog(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        async function testPing() {
            try {
                const response = await fetch('/ping');
                const data = await response.json();
                showResult('server-results', `Ping测试成功: ${JSON.stringify(data)}`, 'success');
            } catch (error) {
                showResult('server-results', `Ping测试失败: ${error.message}`, 'error');
            }
        }

        async function testDownload() {
            try {
                const response = await fetch('/download-test?size=1024');
                showResult('server-results', `下载测试成功: 状态码 ${response.status}`, 'success');
            } catch (error) {
                showResult('server-results', `下载测试失败: ${error.message}`, 'error');
            }
        }

        async function testUpload() {
            try {
                const testData = new ArrayBuffer(1024);
                const response = await fetch('/upload-test', {
                    method: 'POST',
                    body: testData,
                    headers: {
                        'Content-Type': 'application/octet-stream'
                    }
                });
                const result = await response.json();
                showResult('server-results', `上传测试成功: ${JSON.stringify(result)}`, 'success');
            } catch (error) {
                showResult('server-results', `上传测试失败: ${error.message}`, 'error');
            }
        }

        function testDOMElements() {
            const elements = [
                'startTest', 'speedCircle', 'speedValue', 'speedType', 'ipAddress',
                'lolPing', 'douyinPing', 'jdPing', 'msPing', 'toutiaoPing'
            ];
            
            let allFound = true;
            elements.forEach(id => {
                const element = document.getElementById(id);
                const found = element !== null;
                if (!found) allFound = false;
                showResult('frontend-results', `元素 ${id}: ${found ? '✓ 存在' : '✗ 缺失'}`, found ? 'success' : 'error');
            });
            
            showResult('frontend-results', `DOM元素检查${allFound ? '全部通过' : '存在问题'}`, allFound ? 'success' : 'error');
        }

        function testSpeedTestClass() {
            if (typeof SpeedTest !== 'undefined') {
                showResult('frontend-results', '✓ SpeedTest类已定义', 'success');
            } else {
                showResult('frontend-results', '✗ SpeedTest类未定义', 'error');
            }
            
            if (window.speedTestInstance) {
                showResult('frontend-results', '✓ SpeedTest实例已创建', 'success');
            } else {
                showResult('frontend-results', '✗ SpeedTest实例未创建', 'error');
            }
        }

        function simulateClick() {
            const startButton = document.getElementById('startTest');
            if (startButton) {
                showResult('frontend-results', '模拟点击测速按钮...', 'info');
                startButton.click();
                
                setTimeout(() => {
                    const speedCircle = document.getElementById('speedCircle');
                    if (speedCircle && speedCircle.classList.contains('active')) {
                        showResult('frontend-results', '✓ 测速已开始，圆环显示正常', 'success');
                    } else {
                        showResult('frontend-results', '✗ 测速未开始或圆环显示异常', 'error');
                    }
                }, 1000);
            } else {
                showResult('frontend-results', '✗ 未找到测速按钮', 'error');
            }
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            console.log('调试页面已加载');
            setTimeout(() => {
                testDOMElements();
                testSpeedTestClass();
            }, 1000);
        });
    </script>
</body>
</html>
